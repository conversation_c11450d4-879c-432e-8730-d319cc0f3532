{% extends "common/base.html" %}
{% load i18n %}
{% load static %}
{% load pipeline_extras %}

{% block title %}{% trans "Candidate Pipeline" %} | Smarch{% endblock %}

{% block extra_css %}
<style>
    .pipeline-container {
        overflow-x: auto;
        padding: 20px 0;
    }

    .pipeline-board {
        display: flex;
        gap: 20px;
        min-width: 100%;
        padding-bottom: 20px;
    }

    .pipeline-column {
        flex: 0 0 280px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        min-height: 500px;
    }

    .pipeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 6px;
        color: white;
        font-weight: 600;
    }

    .pipeline-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.85em;
    }

    .candidate-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        cursor: move;
        transition: all 0.2s ease;
        border-left: 4px solid #dee2e6;
        position: relative;
    }

    .candidate-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
        cursor: pointer;
    }

    .candidate-actions {
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }

    .candidate-card:hover .candidate-actions {
        opacity: 1;
    }

    .candidate-card.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }

    .candidate-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .candidate-info {
        font-size: 0.9em;
        color: #666;
        margin-bottom: 8px;
    }

    .candidate-priority {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: 500;
    }

    .priority-high { background: #fee; color: #c53030; }
    .priority-medium { background: #fef5e7; color: #d69e2e; }
    .priority-low { background: #f0fff4; color: #38a169; }

    .add-candidate-btn {
        width: 100%;
        padding: 10px;
        border: 2px dashed #dee2e6;
        background: transparent;
        border-radius: 6px;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .add-candidate-btn:hover {
        border-color: #007bff;
        color: #007bff;
        background: rgba(0, 123, 255, 0.05);
    }

    .drop-zone {
        min-height: 100px;
        border: 2px dashed transparent;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .drop-zone.drag-over {
        border-color: #007bff;
        background: rgba(0, 123, 255, 0.05);
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .pipeline-board {
            flex-direction: column;
        }

        .pipeline-column {
            flex: none;
            width: 100%;
            margin-bottom: 20px;
        }

        .candidate-card {
            cursor: pointer;
        }
    }

    /* Stage colors */
    .stage-new { background: #17a2b8; }
    .stage-screening { background: #ffc107; }
    .stage-interview { background: #fd7e14; }
    .stage-offer { background: #6f42c1; }
    .stage-hired { background: #28a745; }
    .stage-rejected { background: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1>{% trans "Candidate Pipeline" %}</h1>
                    <p class="text-muted">{% trans "Manage your recruitment pipeline with drag-and-drop functionality" %}</p>
                </div>
                <div>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCandidateModal">
                        <i class="bi bi-plus-lg"></i> {% trans "Add Candidate" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="pipeline-container">
        <div class="pipeline-board" id="pipelineBoard">
            {% for stage in stages %}
            <div class="pipeline-column" data-stage-id="{{ stage.id }}">
                <div class="pipeline-header stage-{{ stage.name }}">
                    <span>{{ stage.display_name }}</span>
                    <span class="pipeline-count">{{ candidates_by_stage|lookup:stage.name|length }}</span>
                </div>

                <div class="drop-zone" data-stage-id="{{ stage.id }}">
                    {% for pipeline_candidate in candidates_by_stage|lookup:stage.name %}
                    <div class="candidate-card"
                         data-candidate-id="{{ pipeline_candidate.candidate.id }}"
                         data-pipeline-id="{{ pipeline_candidate.id }}"
                         draggable="true">
                        <div class="candidate-name">
                            {{ pipeline_candidate.candidate.user.get_full_name|default:pipeline_candidate.candidate.user.username }}
                        </div>
                        <div class="candidate-info">
                            {% if pipeline_candidate.candidate.industry %}
                                <i class="bi bi-briefcase"></i> {{ pipeline_candidate.candidate.industry }}
                            {% endif %}
                            {% if pipeline_candidate.candidate.location %}
                                <br><i class="bi bi-geo-alt"></i> {{ pipeline_candidate.candidate.location }}
                            {% endif %}
                            {% if pipeline_candidate.candidate.role %}
                                <br><i class="bi bi-person-badge"></i> {{ pipeline_candidate.candidate.role }}
                            {% endif %}
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="candidate-priority priority-{{ pipeline_candidate.priority }}">
                                {{ pipeline_candidate.get_priority_display }}
                            </span>
                            <small class="text-muted">
                                {{ pipeline_candidate.updated_at|date:"M d" }}
                            </small>
                        </div>
                        <div class="candidate-actions mt-2">
                            <small class="text-muted">
                                <i class="bi bi-cursor-fill"></i> Click for details • Drag to move
                            </small>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox fs-3"></i>
                        <p class="mt-2">{% trans "No candidates in this stage" %}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Add Candidate Modal -->
<div class="modal fade" id="addCandidateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Candidate to Pipeline" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCandidateForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="candidateSelect" class="form-label">{% trans "Select Candidate" %}</label>
                        <select class="form-select" id="candidateSelect" name="candidate_id" required>
                            <option value="">{% trans "Choose a candidate..." %}</option>
                            {% for candidate in available_candidates %}
                            <option value="{{ candidate.id }}">
                                {{ candidate.user.get_full_name|default:candidate.user.username }}
                                {% if candidate.industry %} - {{ candidate.industry }}{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="candidateNotes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="candidateNotes" name="notes" rows="3"
                                  placeholder="{% trans 'Add any initial notes about this candidate...' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="addCandidateBtn">{% trans "Add to Pipeline" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Candidate Details Modal -->
<div class="modal fade" id="candidateDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Candidate Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="candidateDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <a href="#" class="btn btn-primary" id="viewFullProfileBtn">{% trans "View Full Profile" %}</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const board = document.getElementById('pipelineBoard');
    let draggedElement = null;
    let isDragging = false;
    let dragStartTime = 0;

    // Add drag and drop functionality
    document.querySelectorAll('.candidate-card').forEach(card => {
        card.addEventListener('dragstart', handleDragStart);
        card.addEventListener('dragend', handleDragEnd);
        card.addEventListener('click', handleCardClick);
    });

    document.querySelectorAll('.drop-zone').forEach(zone => {
        zone.addEventListener('dragover', handleDragOver);
        zone.addEventListener('drop', handleDrop);
        zone.addEventListener('dragenter', handleDragEnter);
        zone.addEventListener('dragleave', handleDragLeave);
    });

    function handleCardClick(e) {
        // Only handle click if we're not dragging
        if (!isDragging) {
            const candidateId = this.dataset.candidateId;
            showCandidateDetails(candidateId);
        }
    }

    function handleDragStart(e) {
        isDragging = true;
        dragStartTime = Date.now();
        draggedElement = this;
        this.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', this.outerHTML);
    }

    function handleDragEnd(e) {
        this.classList.remove('dragging');
        draggedElement = null;

        // Reset dragging state after a short delay to prevent click events
        setTimeout(() => {
            isDragging = false;
        }, 100);
    }

    function handleDragOver(e) {
        if (e.preventDefault) {
            e.preventDefault();
        }
        e.dataTransfer.dropEffect = 'move';
        return false;
    }

    function handleDragEnter(e) {
        this.classList.add('drag-over');
    }

    function handleDragLeave(e) {
        this.classList.remove('drag-over');
    }

    function handleDrop(e) {
        if (e.stopPropagation) {
            e.stopPropagation();
        }

        this.classList.remove('drag-over');

        if (draggedElement !== null) {
            const candidateId = draggedElement.dataset.candidateId;
            const newStageId = this.dataset.stageId;

            // Remove "no candidates" message if it exists
            const noCandidate = this.querySelector('.text-center.text-muted.py-4');
            if (noCandidate) {
                noCandidate.remove();
            }

            // Move the element to the TOP of the column
            const firstCard = this.querySelector('.candidate-card');
            if (firstCard) {
                this.insertBefore(draggedElement, firstCard);
            } else {
                this.appendChild(draggedElement);
            }

            // Send AJAX request to update backend
            moveCandidateStage(candidateId, newStageId);
        }

        return false;
    }

    function moveCandidateStage(candidateId, stageId) {
        const formData = new FormData();
        formData.append('candidate_id', candidateId);
        formData.append('stage_id', stageId);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

        fetch('{% url "services:move_candidate_stage" %}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                updateStageCounts();
            } else {
                showToast(data.error, 'error');
                // Revert the move if it failed
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while moving the candidate', 'error');
            location.reload();
        });
    }

    // Add candidate to pipeline
    document.getElementById('addCandidateBtn').addEventListener('click', function() {
        const form = document.getElementById('addCandidateForm');
        const formData = new FormData(form);

        fetch('{% url "services:add_candidate_to_pipeline" %}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('addCandidateModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while adding the candidate', 'error');
        });
    });

    function updateStageCounts() {
        document.querySelectorAll('.pipeline-column').forEach(column => {
            const count = column.querySelectorAll('.candidate-card').length;
            const countElement = column.querySelector('.pipeline-count');
            if (countElement) {
                countElement.textContent = count;
            }

            // Show/hide "no candidates" message
            const dropZone = column.querySelector('.drop-zone');
            const noCandidate = dropZone.querySelector('.text-center.text-muted.py-4');
            const hasCards = count > 0;

            if (!hasCards && !noCandidate) {
                // Add "no candidates" message
                const noMessage = document.createElement('div');
                noMessage.className = 'text-center text-muted py-4';
                noMessage.innerHTML = '<i class="bi bi-inbox fs-3"></i><p class="mt-2">{% trans "No candidates in this stage" %}</p>';
                dropZone.appendChild(noMessage);
            } else if (hasCards && noCandidate) {
                // Remove "no candidates" message
                noCandidate.remove();
            }
        });
    }

    // Candidate details function (global scope)
    window.showCandidateDetails = function(candidateId) {
        const modal = new bootstrap.Modal(document.getElementById('candidateDetailsModal'));
        const content = document.getElementById('candidateDetailsContent');
        const viewBtn = document.getElementById('viewFullProfileBtn');

        // Show loading
        content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';

        // Set the view full profile link
        viewBtn.href = `/services/applicants/${candidateId}/`;

        // Show modal
        modal.show();

        // Load candidate details (simplified for now)
        setTimeout(() => {
            // Find the candidate card to get basic info
            const candidateCard = document.querySelector(`[data-candidate-id="${candidateId}"]`);
            const candidateName = candidateCard.querySelector('.candidate-name').textContent;
            const candidateInfo = candidateCard.querySelector('.candidate-info').innerHTML;
            const priority = candidateCard.querySelector('.candidate-priority').textContent;

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-8">
                        <h5>${candidateName}</h5>
                        <div class="mb-3">${candidateInfo}</div>
                        <div class="mb-3">
                            <span class="badge bg-info">${priority} Priority</span>
                        </div>
                        <div class="mb-3">
                            <h6>Quick Actions:</h6>
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="moveToStage(${candidateId}, 'interview')">
                                <i class="bi bi-calendar"></i> Schedule Interview
                            </button>
                            <button class="btn btn-sm btn-outline-success me-2" onclick="moveToStage(${candidateId}, 'offer')">
                                <i class="bi bi-hand-thumbs-up"></i> Make Offer
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="moveToStage(${candidateId}, 'rejected')">
                                <i class="bi bi-x-circle"></i> Reject
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Pipeline History</h6>
                                <p class="card-text small text-muted">
                                    <i class="bi bi-clock"></i> Added to pipeline recently<br>
                                    <i class="bi bi-arrow-right"></i> Current stage updates automatically
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }, 500);
    }

    // Quick stage move function (global scope)
    window.moveToStage = function(candidateId, stageName) {
        const stageElement = document.querySelector(`[data-stage-id]`);
        // This is a simplified implementation - in a real app you'd find the correct stage ID
        showToast(`Moving candidate to ${stageName} stage...`, 'info');

        // Close the modal
        bootstrap.Modal.getInstance(document.getElementById('candidateDetailsModal')).hide();
    }

    function showToast(message, type) {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Mobile touch support (basic)
    if ('ontouchstart' in window) {
        document.querySelectorAll('.candidate-card').forEach(card => {
            card.addEventListener('click', function() {
                // On mobile, show a simple menu to move between stages
                const candidateId = this.dataset.candidateId;
                const candidateName = this.querySelector('.candidate-name').textContent;

                if (confirm(`Move ${candidateName} to a different stage?`)) {
                    // Simple implementation - could be enhanced with a proper mobile interface
                    const stages = ['new', 'screening', 'interview', 'offer', 'hired', 'rejected'];
                    const stageNames = ['New', 'Screening', 'Interview', 'Offer', 'Hired', 'Rejected'];

                    let stageChoice = prompt(`Choose stage for ${candidateName}:\n` +
                        stageNames.map((name, i) => `${i + 1}. ${name}`).join('\n'));

                    if (stageChoice && stageChoice >= 1 && stageChoice <= 6) {
                        const stageElement = document.querySelector(`[data-stage-id]`);
                        // This is a simplified mobile implementation
                        // In a real app, you'd want a proper mobile-friendly interface
                    }
                }
            });
        });
    }
});
</script>
{% endblock %}
