from django.db import models
from django.contrib.auth import get_user_model
from users.models import ApplicantProfile, HiringPartnerProfile

User = get_user_model()


class Offer(models.Model):
    """Model representing a service offer."""
    title = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    is_premium = models.BooleanField(default=False)
    hiring_partner = models.ForeignKey(
        HiringPartnerProfile, on_delete=models.CASCADE, related_name="offers"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Offer'
        verbose_name_plural = 'Offers'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class Subscription(models.Model):
    """Model representing a subscription to a service."""
    hiring_partner = models.Foreign<PERSON>ey(
        HiringPartnerProfile, on_delete=models.CASCADE, related_name="subscriptions"
    )
    plan = models.CharField(
        max_length=20,
        choices=[
            ('basic', 'Basic'),
            ('standard', 'Standard'),
            ('premium', 'Premium'),
            ('enterprise', 'Enterprise')
        ]
    )
    start_date = models.DateField()
    end_date = models.DateField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('cancelled', 'Cancelled'),
            ('expired', 'Expired'),
            ('pending', 'Pending')
        ],
        default='pending'
    )
    is_auto_renew = models.BooleanField(default=True)
    payment_method = models.CharField(max_length=100, blank=True, null=True)
    # Stripe-specific fields
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_subscription_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_payment_method_id = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Subscription'
        verbose_name_plural = 'Subscriptions'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.hiring_partner.company_name} - {self.plan} Plan"


class ContactRequest(models.Model):
    """Model representing contact requests between hiring partners and applicants."""
    sender = models.ForeignKey(
        HiringPartnerProfile, on_delete=models.CASCADE, related_name="sent_requests"
    )
    recipient = models.ForeignKey(
        ApplicantProfile, on_delete=models.CASCADE, related_name="received_requests"
    )
    message = models.TextField()
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('accepted', 'Accepted'),
            ('declined', 'Declined')
        ],
        default='pending'
    )
    sent_at = models.DateTimeField(auto_now_add=True)
    responded_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = 'Contact Request'
        verbose_name_plural = 'Contact Requests'
        ordering = ['-sent_at']

    def __str__(self):
        return f"Request from {self.sender.company_name} to {self.recipient.user.username}"


class Payment(models.Model):
    """Model representing payments for subscriptions."""
    subscription = models.ForeignKey(
        Subscription, on_delete=models.CASCADE, related_name="payments"
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('succeeded', 'Succeeded'),
            ('failed', 'Failed'),
            ('refunded', 'Refunded')
        ]
    )
    transaction_id = models.CharField(max_length=100, unique=True)
    payment_method = models.CharField(max_length=50, blank=True, null=True)
    billing_details = models.JSONField(blank=True, null=True)
    # Stripe-specific fields
    stripe_payment_intent_id = models.CharField(max_length=100, blank=True, null=True)
    stripe_charge_id = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Payment'
        verbose_name_plural = 'Payments'
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment {self.transaction_id} - {self.amount}"
